# Run the api from the cmd '$env:ENVIRONMENT='local'; env_ocr\Scripts\uvicorn.exe src.api:app --host 0.0.0.0 --port 8088 --log-config=logging.yaml '  # dev
# Run the api from the cmd 'uvicorn src.api:app --host 0.0.0.0 --port 8088 --log-config=logging.yaml --env-file .env.local '  # dev

# Run the api from the cmd '$env:ENVIRONMENT='preprod'; env_ocr\Scripts\uvicorn.exe src.api:app --host 0.0.0.0 --port 8090 --log-config=logging.yaml '  # preprod
# Run the api from the cmd 'uvicorn src.api:app --host 0.0.0.0 --port 8089 --log-config=logging.yaml --env-file .env.preprod '  # preprod

# Run the api from the cmd '$env:ENVIRONMENT='prod'; env_ocr\Scripts\uvicorn.exe src.api:app --host 0.0.0.0 --port 8089 --log-config=logging.yaml '  # prod
# Run the api from the cmd 'uvicorn src.api:app --host 0.0.0.0 --port 8000 --log-config=logging.yaml --env-file .env.prod '  # prod

# Suppress annoying warnings BEFORE importing other modules
import warnings
warnings.filterwarnings("ignore", message="Field .* has conflict with protected namespace .*", category=UserWarning)
warnings.filterwarnings("ignore", message="You are using `torch.load` with `weights_only=False`.*", category=FutureWarning)

from fastapi.exceptions import RequestValidationError
from src.app.routes import (smart_crop, magic_pro_filter, identify_supplier, process_image_supp,
                            process_ocr_multi, login, logout, send_data_to_winplus, realtime_contours,
                            ocr_to_winplus, ocr_to_windoc, suppliers, medicament_ocr_tap, health, root_names_sync)
from src.app.services.websocket_manager import router as websocket_router
from src.app.dependencies import get_current_user, check_headers, check_headers_dynamic, check_tenant_user_headers_winplus
import uvicorn
from fastapi import FastAPI, Depends, Request
from fastapi.responses import JSONResponse
import logging
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from src.app.config import API_URL, ENVIRONMENT  # Import ENVIRONMENT from config
import sys
from pathlib import Path
from src.app.database import init_db
from logging.handlers import RotatingFileHandler
import warnings

# Initialize the dual database system (PostgreSQL + SQLite)
try:
    init_db()
    print("Dual database system initialized successfully")
except Exception as e:
    print(f"Failed to initialize databases: {e}")
    # Continue with application startup - the system can still work with fallback

# Initialize the scheduler service
try:
    from src.app.services.scheduler_service import start_scheduler, setup_weekly_sync
    start_scheduler()
    # Setup weekly sync for Sunday at 2 AM UTC
    setup_weekly_sync('sunday', 2, 0)
    print("Scheduler service initialized successfully")
except Exception as e:
    print(f"Failed to initialize scheduler: {e}")
    # Continue with application startup - scheduler is optional

# Define log file path
log_file_path = Path(__file__).resolve().parent.parent / "log_app_ocr_grossiste_documents.log"

# Only configure logging if not using external log config (like logging.yaml)
# This prevents conflicts when uvicorn uses --log-config parameter
if not any('--log-config' in arg for arg in sys.argv):
    # Logging format with timestamp
    LOG_FORMAT = "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
    DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

    # Create and configure the rotating file handler
    file_handler = RotatingFileHandler(
        log_file_path,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setFormatter(logging.Formatter(LOG_FORMAT, DATE_FORMAT))

    # Create and configure the console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter(LOG_FORMAT, DATE_FORMAT))

    # Configure the root logger
    logging.basicConfig(
        level=logging.INFO,
        handlers=[
            console_handler,
            file_handler
        ]
    )

    # Get uvicorn loggers and set their handlers
    uvicorn_access = logging.getLogger("uvicorn.access")
    uvicorn_access.handlers = []  # Clear existing handlers
    uvicorn_access.addHandler(file_handler)
    uvicorn_access.addHandler(console_handler)

    uvicorn_error = logging.getLogger("uvicorn.error")
    uvicorn_error.handlers = []  # Clear existing handlers
    uvicorn_error.addHandler(file_handler)
    uvicorn_error.addHandler(console_handler)

# Capture remaining warnings into the logging system
logging.captureWarnings(True)
warnings.simplefilter("default")

# Create logger for this module
logger = logging.getLogger(__name__)

# Test log file writing
try:
    logger.info(f"Starting application  in {ENVIRONMENT.upper()} environment ...")
    print(f"Starting application  in {ENVIRONMENT.upper()} environment ...")
    logger.info(f"Log file path: {log_file_path}")
    logger.info("Logging is fully configured. [OK]")
except Exception as e:
    logger.error(f"Failed to write to log file: {e}")


if ENVIRONMENT == 'prod':
    # Disable Swagger UI and ReDoc in production
    app = FastAPI(docs_url=None, redoc_url=None)
else:
    # Keep documentation in non-production environments
    app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["Authorization", "AuthorizationTenant", "AuthorizationUser", "Authorizationtenant", "Authorizationuser", "Content-Type"]
)

# Mount the 'temp' directory at the '/static' path
app.mount("/static", StaticFiles(directory="temp"), name="static")

# Define the API URL
apiUrl = API_URL



# Exception Handler for 422 Errors
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    logger.error(f"Validation Error: {exc.errors()}")  # Logs detailed validation errors
    return JSONResponse(
        status_code=422,
        content={"detail": exc.errors()},
    )

@app.get("/")
def read_root():
    return {"Hello": "World"}


# Include routers
app.include_router(websocket_router, prefix="")
app.include_router(realtime_contours.router, prefix="")  # dependencies=[Depends(get_current_user), Depends(check_headers)]

# Health check routes (no authentication required)
app.include_router(health.router, prefix="")

# Login routes without authentication
app.include_router(login.router, prefix="")

# Protected routes
app.include_router(logout.router, prefix="/logout", dependencies=[Depends(get_current_user), Depends(check_headers_dynamic)])
app.include_router(smart_crop.router, prefix="/smart_crop", dependencies=[Depends(get_current_user), Depends(check_headers_dynamic)])
app.include_router(magic_pro_filter.router, prefix="/magic_pro_filter", dependencies=[Depends(get_current_user), Depends(check_headers_dynamic)])
app.include_router(identify_supplier.router, prefix="/identify_supplier", dependencies=[Depends(get_current_user), Depends(check_headers_dynamic)])
app.include_router(process_image_supp.router, prefix="/process_image_supp", dependencies=[Depends(get_current_user), Depends(check_headers_dynamic)])
app.include_router(process_ocr_multi.router, prefix="/process_ocr_multi", dependencies=[Depends(get_current_user), Depends(check_headers_dynamic)])
app.include_router(suppliers.router, prefix="/suppliers")
app.include_router(ocr_to_winplus.router, prefix="/winplus")
app.include_router(ocr_to_windoc.router, prefix="/windoc")

# Medicament Scan with TAP
app.include_router(medicament_ocr_tap.router, prefix="/medicament_ocr_tap", dependencies=[Depends(get_current_user), Depends(check_headers_dynamic)])

# Root Names Sync Management
app.include_router(root_names_sync.router, prefix="/root_names_sync", dependencies=[Depends(get_current_user), Depends(check_headers_dynamic)])

# app.include_router(send_data_to_winplus.router, prefix="/send_data_to_winPlus", dependencies=[Depends(get_current_user), Depends(check_headers)])


@app.exception_handler(Exception)
def validation_exception_handler(request, exc):
    return JSONResponse(
        status_code=400,
        content={"message": str(exc)}
    )


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8083)
