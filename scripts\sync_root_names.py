#!/usr/bin/env python3
"""
Root Names Sync Script

This script allows you to run the root names synchronization from the terminal.
It can test connections, run sync, and check status.

Usage:
    python scripts/sync_root_names.py --help
    python scripts/sync_root_names.py --test-connection
    python scripts/sync_root_names.py --run-sync
    python scripts/sync_root_names.py --status
"""

import sys
import os
import argparse
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_connection():
    """Test connection to external database."""
    try:
        from src.app.services.root_names_sync_service import test_sync_connection
        
        logger.info("Testing connection to external database...")
        result = test_sync_connection()
        
        if result.get('connection_healthy', False):
            logger.info("✅ Connection test successful!")
            logger.info(f"Total product names available: {result.get('total_count', 'Unknown')}")
            logger.info(f"Sample names: {result.get('sample_names', [])[:5]}")
        else:
            logger.error("❌ Connection test failed!")
            logger.error(f"Error: {result.get('error', 'Unknown error')}")
            
        return result.get('connection_healthy', False)
        
    except Exception as e:
        logger.error(f"❌ Connection test failed with exception: {e}")
        return False


def run_sync():
    """Run the root names synchronization."""
    try:
        from src.app.services.root_names_sync_service import perform_sync
        
        logger.info("Starting root names synchronization...")
        result = perform_sync()
        
        if result.get('success', False):
            logger.info("✅ Synchronization completed successfully!")
            stats = result.get('stats', {})
            logger.info(f"Words before: {stats.get('total_words_before', 'Unknown')}")
            logger.info(f"Words after: {stats.get('total_words_after', 'Unknown')}")
            logger.info(f"Duration: {stats.get('sync_duration', 'Unknown')} seconds")
        else:
            logger.error("❌ Synchronization failed!")
            logger.error(f"Error: {result.get('message', 'Unknown error')}")
            
        return result.get('success', False)
        
    except Exception as e:
        logger.error(f"❌ Synchronization failed with exception: {e}")
        return False


def get_status():
    """Get the current sync status."""
    try:
        from src.app.services.root_names_sync_service import get_sync_status
        from src.app.utils.root_names_constants import get_correct_words_count
        
        logger.info("Getting sync status...")
        status = get_sync_status()
        
        logger.info("📊 Current Status:")
        logger.info(f"Current word count: {get_correct_words_count()}")
        logger.info(f"Last sync: {status.get('last_sync_time', 'Never')}")
        
        sync_stats = status.get('sync_stats', {})
        logger.info(f"Last sync status: {sync_stats.get('status', 'Unknown')}")
        
        if sync_stats.get('last_sync'):
            logger.info(f"Last sync duration: {sync_stats.get('sync_duration', 'Unknown')} seconds")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to get status: {e}")
        return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description='Root Names Sync Management Script',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python scripts/sync_root_names.py --test-connection
    python scripts/sync_root_names.py --run-sync
    python scripts/sync_root_names.py --status
    python scripts/sync_root_names.py --all
        """
    )
    
    parser.add_argument(
        '--test-connection', 
        action='store_true',
        help='Test connection to external database'
    )
    
    parser.add_argument(
        '--run-sync', 
        action='store_true',
        help='Run root names synchronization'
    )
    
    parser.add_argument(
        '--status', 
        action='store_true',
        help='Get current sync status'
    )
    
    parser.add_argument(
        '--all', 
        action='store_true',
        help='Run all operations: test connection, get status, and run sync'
    )
    
    args = parser.parse_args()
    
    # If no arguments provided, show help
    if not any(vars(args).values()):
        parser.print_help()
        return
    
    success = True
    
    try:
        if args.all:
            logger.info("🚀 Running all operations...")
            success &= test_connection()
            success &= get_status()
            if success:
                success &= run_sync()
        else:
            if args.test_connection:
                success &= test_connection()
            
            if args.status:
                success &= get_status()
            
            if args.run_sync:
                success &= run_sync()
        
        if success:
            logger.info("✅ All operations completed successfully!")
        else:
            logger.error("❌ Some operations failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("🛑 Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
