"""
Root Names Sync Routes

API routes for managing the synchronization of product root names.
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import Dict, Any, Optional
import logging

from src.app.dependencies import get_current_user, check_headers_dynamic
from src.app.services.scheduler_service import (
    setup_weekly_sync, run_sync_now, get_scheduler_status
)
from src.app.services.root_names_sync_service import (
    get_sync_status, test_sync_connection
)

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/status", dependencies=[Depends(get_current_user), Depends(check_headers_dynamic)])
async def get_root_names_sync_status():
    """
    Get the current status of root names synchronization.
    
    Returns:
        Dict[str, Any]: Current sync status and scheduler information
    """
    try:
        # Get sync status
        sync_status = get_sync_status()
        
        # Get scheduler status
        scheduler_status = get_scheduler_status()
        
        return {
            'sync_status': sync_status,
            'scheduler_status': scheduler_status
        }
        
    except Exception as e:
        logger.error(f"Failed to get sync status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get sync status: {str(e)}")


@router.post("/run", dependencies=[Depends(get_current_user), Depends(check_headers_dynamic)])
async def run_root_names_sync(background_tasks: BackgroundTasks):
    """
    Run the root names synchronization immediately.
    
    Args:
        background_tasks: FastAPI background tasks
        
    Returns:
        Dict[str, Any]: Sync initiation status
    """
    try:
        # Run sync in background
        background_tasks.add_task(run_sync_now)
        
        return {
            'success': True,
            'message': 'Root names synchronization started in background',
            'status': 'running'
        }
        
    except Exception as e:
        logger.error(f"Failed to start sync: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start sync: {str(e)}")


@router.post("/schedule", dependencies=[Depends(get_current_user), Depends(check_headers_dynamic)])
async def schedule_root_names_sync(
    day_of_week: str = 'sunday',
    hour: int = 2,
    minute: int = 0
):
    """
    Schedule the weekly root names synchronization.
    
    Args:
        day_of_week (str): Day of the week to run (default: 'sunday')
        hour (int): Hour to run (default: 2 AM)
        minute (int): Minute to run (default: 0)
        
    Returns:
        Dict[str, Any]: Scheduling status
    """
    try:
        # Validate inputs
        valid_days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
        if day_of_week.lower() not in valid_days:
            raise HTTPException(status_code=400, detail=f"Invalid day of week. Must be one of: {', '.join(valid_days)}")
            
        if not (0 <= hour <= 23):
            raise HTTPException(status_code=400, detail="Hour must be between 0 and 23")
            
        if not (0 <= minute <= 59):
            raise HTTPException(status_code=400, detail="Minute must be between 0 and 59")
        
        # Schedule the job
        job_id = setup_weekly_sync(day_of_week.lower(), hour, minute)
        
        return {
            'success': True,
            'message': f'Root names sync scheduled for {day_of_week} at {hour:02d}:{minute:02d} UTC',
            'job_id': job_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to schedule sync: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to schedule sync: {str(e)}")


@router.get("/test-connection", dependencies=[Depends(get_current_user), Depends(check_headers_dynamic)])
async def test_root_names_sync_connection():
    """
    Test the connection to the external database.
    
    Returns:
        Dict[str, Any]: Connection test results
    """
    try:
        # Test connection
        test_result = test_sync_connection()
        
        return {
            'success': test_result.get('connection_healthy', False),
            'test_result': test_result
        }
        
    except Exception as e:
        logger.error(f"Connection test failed: {e}")
        raise HTTPException(status_code=500, detail=f"Connection test failed: {str(e)}")
