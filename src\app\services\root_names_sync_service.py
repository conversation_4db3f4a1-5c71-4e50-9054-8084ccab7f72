"""
Root Names Sync Service

This service handles the synchronization of product root names from the external database
to the local constants file. It fetches data from winproduit_master database and updates
the CORRECT_WORDS array in root_names_constants.py.
"""

import logging
import os
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path

from src.app.utils.external_db_connection import fetch_product_root_names, test_external_connection
from src.app.utils.root_names_constants import update_correct_words, get_correct_words_count

logger = logging.getLogger(__name__)


class RootNamesSyncService:
    """Service for syncing product root names from external database."""

    def __init__(self):
        self.last_sync_time = None
        self.sync_stats = {
            'last_sync': None,
            'total_words_before': 0,
            'total_words_after': 0,
            'new_words_added': 0,
            'words_removed': 0,
            'sync_duration': 0,
            'status': 'never_synced'
        }

    def sync_root_names(self) -> Dict[str, Any]:
        """
        Perform the synchronization of root names from external database.
        
        Returns:
            Dict[str, Any]: Sync results and statistics
        """
        start_time = datetime.now()
        logger.info("Starting root names synchronization...")

        try:
            # Test external database connection first
            connection_test = test_external_connection()
            if not connection_test.get('connection_healthy', False):
                raise Exception(f"External database connection failed: {connection_test.get('error', 'Unknown error')}")

            # Get current word count before sync
            words_before = get_correct_words_count()
            self.sync_stats['total_words_before'] = words_before
            logger.info(f"Current word count before sync: {words_before}")

            # Fetch new root names from external database
            logger.info("Fetching product root names from external database...")
            new_root_names = fetch_product_root_names()

            if not new_root_names:
                raise Exception("No root names fetched from external database")

            logger.info(f"Fetched {len(new_root_names)} root names from external database")

            # Update the constants file
            logger.info("Updating root names constants...")
            success = update_correct_words(new_root_names)

            if not success:
                raise Exception("Failed to update root names constants")

            # Get word count after sync
            words_after = get_correct_words_count()
            self.sync_stats['total_words_after'] = words_after

            # Calculate statistics
            end_time = datetime.now()
            sync_duration = (end_time - start_time).total_seconds()

            # Update sync statistics
            self.sync_stats.update({
                'last_sync': end_time.isoformat(),
                'new_words_added': max(0, words_after - words_before),
                'words_removed': max(0, words_before - words_after),
                'sync_duration': sync_duration,
                'status': 'success'
            })

            self.last_sync_time = end_time

            # Update the constants file with sync metadata
            self._update_file_metadata(end_time, len(new_root_names))

            logger.info(f"Root names synchronization completed successfully in {sync_duration:.2f} seconds")
            logger.info(f"Words before: {words_before}, Words after: {words_after}")

            return {
                'success': True,
                'message': 'Root names synchronized successfully',
                'stats': self.sync_stats.copy(),
                'external_db_stats': connection_test
            }

        except Exception as e:
            end_time = datetime.now()
            sync_duration = (end_time - start_time).total_seconds()
            
            self.sync_stats.update({
                'last_sync': end_time.isoformat(),
                'sync_duration': sync_duration,
                'status': 'failed',
                'error': str(e)
            })

            logger.error(f"Root names synchronization failed: {e}")
            
            return {
                'success': False,
                'message': f'Root names synchronization failed: {e}',
                'stats': self.sync_stats.copy()
            }

    def _update_file_metadata(self, sync_time: datetime, word_count: int):
        """
        Update the metadata in the root_names_constants.py file.
        
        Args:
            sync_time (datetime): Time of the sync
            word_count (int): Number of words synced
        """
        try:
            constants_file_path = Path(__file__).parent.parent / "utils" / "root_names_constants.py"
            
            if not constants_file_path.exists():
                logger.warning(f"Constants file not found at {constants_file_path}")
                return

            # Read the current file content
            with open(constants_file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Update the metadata comment
            new_metadata = f"Last updated: {sync_time.strftime('%Y-%m-%d %H:%M:%S')} UTC - {word_count} words synced"
            
            # Replace the metadata line
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if line.strip().startswith('Last updated:'):
                    lines[i] = f"Last updated: {new_metadata}"
                    break
            else:
                # If metadata line not found, add it after the module docstring
                for i, line in enumerate(lines):
                    if '"""' in line and i > 0:  # Find the end of docstring
                        lines.insert(i + 1, f"\n{new_metadata}")
                        break

            # Write the updated content back
            with open(constants_file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))

            logger.info("Updated metadata in root_names_constants.py")

        except Exception as e:
            logger.error(f"Failed to update file metadata: {e}")

    def get_sync_status(self) -> Dict[str, Any]:
        """
        Get the current sync status and statistics.
        
        Returns:
            Dict[str, Any]: Current sync status and stats
        """
        return {
            'last_sync_time': self.last_sync_time.isoformat() if self.last_sync_time else None,
            'current_word_count': get_correct_words_count(),
            'sync_stats': self.sync_stats.copy()
        }

    def test_sync_connection(self) -> Dict[str, Any]:
        """
        Test the connection to external database without performing sync.
        
        Returns:
            Dict[str, Any]: Connection test results
        """
        logger.info("Testing external database connection...")
        return test_external_connection()


# Global sync service instance
sync_service = RootNamesSyncService()


def perform_sync() -> Dict[str, Any]:
    """
    Convenience function to perform root names sync.
    
    Returns:
        Dict[str, Any]: Sync results
    """
    return sync_service.sync_root_names()


def get_sync_status() -> Dict[str, Any]:
    """
    Convenience function to get sync status.
    
    Returns:
        Dict[str, Any]: Current sync status
    """
    return sync_service.get_sync_status()


def test_sync_connection() -> Dict[str, Any]:
    """
    Convenience function to test sync connection.
    
    Returns:
        Dict[str, Any]: Connection test results
    """
    return sync_service.test_sync_connection()
