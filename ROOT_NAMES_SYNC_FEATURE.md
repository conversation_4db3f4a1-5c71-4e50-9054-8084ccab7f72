# 🔄 Root Names Sync Feature Documentation

## 📋 Overview

This feature implements an automated weekly synchronization system for product root names from the external database `winproduit_master` to the local OCR system. It replaces the static `CORRECT_WORDS` array with a dynamic, auto-updating system.

## 🏗️ Architecture

### **Files Created/Modified:**

1. **`src/app/utils/root_names_constants.py`** - New constants file for CORRECT_WORDS
2. **`src/app/utils/external_db_connection.py`** - External database connection utility
3. **`src/app/services/root_names_sync_service.py`** - Main sync service logic
4. **`src/app/services/scheduler_service.py`** - APScheduler management
5. **`src/app/routes/root_names_sync.py`** - API endpoints for sync management
6. **`scripts/sync_root_names.py`** - Terminal script for manual operations
7. **`src/app/config.py`** - Added external database configuration
8. **`src/api.py`** - Integrated scheduler and routes
9. **`requirements.txt`** - Added APScheduler dependency

### **Files Updated:**
- **`src/app/ocr/table.py`** - Updated imports to use new constants
- **`src/app/services/medication_processor.py`** - Updated imports to use new constants

## 🔧 Configuration

### **External Database Settings** (in `src/app/config.py`):
```python
EXTERNAL_POSTGRES_HOST = 'vps6.sophatel.com'
EXTERNAL_POSTGRES_PORT = '5432'
EXTERNAL_POSTGRES_DB = 'winproduit_master'
EXTERNAL_POSTGRES_USER = 'postgres'
EXTERNAL_POSTGRES_PASSWORD = 'JX4yxIN7fBiDDonrf3tSXhVxoS7B'
```

### **SQL Query Used:**
```sql
SELECT DISTINCT p.nom_racine FROM produit p 
WHERE p.nom_racine IS NOT NULL AND p.nom_racine != ''
```

## ⏰ Scheduling System

### **Default Schedule:**
- **Frequency**: Weekly
- **Day**: Sunday
- **Time**: 2:00 AM UTC
- **Scheduler**: APScheduler (Background)

### **How It Works:**
1. **Automatic Startup**: Scheduler starts when FastAPI application launches
2. **Weekly Execution**: Runs every Sunday at 2 AM UTC automatically
3. **Background Processing**: Non-blocking execution
4. **Error Handling**: Continues running even if one sync fails
5. **Logging**: Comprehensive logs for monitoring

### **Scheduler Features:**
- **Persistent**: Survives application restarts
- **Configurable**: Can change schedule via API
- **Monitored**: Job execution tracking and error reporting
- **Manual Override**: Can trigger immediate sync

## 🚀 API Endpoints

### **Base URL**: `http://localhost:8088/root_names_sync`

### **1. Test Connection**
```http
GET /root_names_sync/test-connection
Headers:
  Authorization: Bearer YOUR_JWT_TOKEN
  AuthorizationTenant: YOUR_TENANT_TOKEN
  Content-Type: application/json
```

**Response:**
```json
{
  "success": true,
  "test_result": {
    "status": "success",
    "total_count": 152847,
    "sample_names": ["PRODUCT1", "PRODUCT2", "..."],
    "connection_healthy": true
  }
}
```

### **2. Get Sync Status**
```http
GET /root_names_sync/status
Headers:
  Authorization: Bearer YOUR_JWT_TOKEN
  AuthorizationTenant: YOUR_TENANT_TOKEN
  Content-Type: application/json
```

**Response:**
```json
{
  "sync_status": {
    "last_sync_time": "2025-01-15T10:30:05",
    "current_word_count": 152847,
    "sync_stats": {
      "last_sync": "2025-01-15T10:30:05",
      "total_words_before": 2018,
      "total_words_after": 152847,
      "sync_duration": 4.2,
      "status": "success"
    }
  },
  "scheduler_status": {
    "scheduler_running": true,
    "total_jobs": 1,
    "jobs": [
      {
        "job_id": "root_names_sync_weekly",
        "name": "Weekly Root Names Sync",
        "next_run_time": "2025-01-19T02:00:00",
        "trigger": "cron[day_of_week='sun', hour='2', minute='0']"
      }
    ]
  }
}
```

### **3. Run Sync Immediately**
```http
POST /root_names_sync/run
Headers:
  Authorization: Bearer YOUR_JWT_TOKEN
  AuthorizationTenant: YOUR_TENANT_TOKEN
  Content-Type: application/json
Body: (empty)
```

**Response:**
```json
{
  "success": true,
  "message": "Root names synchronization started in background",
  "status": "running"
}
```

### **4. Change Schedule**
```http
POST /root_names_sync/schedule?day_of_week=sunday&hour=2&minute=0
Headers:
  Authorization: Bearer YOUR_JWT_TOKEN
  AuthorizationTenant: YOUR_TENANT_TOKEN
  Content-Type: application/json
```

**Parameters:**
- `day_of_week`: monday, tuesday, wednesday, thursday, friday, saturday, sunday
- `hour`: 0-23 (24-hour format)
- `minute`: 0-59

**Response:**
```json
{
  "success": true,
  "message": "Root names sync scheduled for sunday at 02:00 UTC",
  "job_id": "root_names_sync_weekly"
}
```

## 💻 Terminal Usage

### **Prerequisites:**
```bash
# Activate virtual environment
env_ocr\Scripts\activate
```

### **Available Commands:**

#### **1. Test Connection**
```bash
python scripts/sync_root_names.py --test-connection
```

#### **2. Check Status**
```bash
python scripts/sync_root_names.py --status
```

#### **3. Run Sync Immediately**
```bash
python scripts/sync_root_names.py --run-sync
```

#### **4. Run All Operations**
```bash
python scripts/sync_root_names.py --all
```

#### **5. Get Help**
```bash
python scripts/sync_root_names.py --help
```

### **Example Terminal Output:**
```
2025-01-15 10:30:00 - INFO - Testing connection to external database...
2025-01-15 10:30:01 - INFO - ✅ Connection test successful!
2025-01-15 10:30:01 - INFO - Total product names available: ***********-01-15 10:30:01 - INFO - Sample names: ['PRODUCT1', 'PRODUCT2', 'PRODUCT3']
2025-01-15 10:30:01 - INFO - Starting root names synchronization...
2025-01-15 10:30:01 - INFO - Current word count before sync: 2018
2025-01-15 10:30:05 - INFO - ✅ Synchronization completed successfully!
2025-01-15 10:30:05 - INFO - Words before: 2018
2025-01-15 10:30:05 - INFO - Words after: ***********-01-15 10:30:05 - INFO - Duration: 4.2 seconds
```

## 🔍 Monitoring & Logging

### **Log Locations:**
- **Application logs**: `log_app_ocr_grossiste_documents.log`
- **Console output**: Real-time during execution

### **Key Metrics Tracked:**
- Sync duration
- Word count before/after
- Connection health
- Error details
- Last sync timestamp

### **Health Checks:**
- Database connectivity
- Query execution
- Data validation
- File write permissions

## 🚨 Error Handling

### **Common Issues & Solutions:**

#### **1. Connection Failed**
```
Error: Failed to initialize external PostgreSQL connection
Solution: Check network connectivity and database credentials
```

#### **2. Authentication Error**
```
Error: FATAL: password authentication failed
Solution: Verify EXTERNAL_POSTGRES_PASSWORD in config
```

#### **3. No Data Retrieved**
```
Error: No root names fetched from external database
Solution: Check if 'produit' table exists and has data
```

#### **4. Permission Error**
```
Error: Failed to update file metadata
Solution: Check write permissions on root_names_constants.py
```

## 🔧 Maintenance

### **Regular Tasks:**
1. **Monitor logs** for sync failures
2. **Check disk space** for log files
3. **Verify schedule** is running correctly
4. **Test connection** periodically

### **Troubleshooting:**
```bash
# Check if scheduler is running
curl http://localhost:8088/root_names_sync/status

# Test database connection
python scripts/sync_root_names.py --test-connection

# Force immediate sync
python scripts/sync_root_names.py --run-sync
```

## 📊 Performance Impact

### **Before Implementation:**
- Static array with ~2,000 words
- Manual updates required
- Outdated product names

### **After Implementation:**
- Dynamic array with ~150,000 words
- Automatic weekly updates
- Always current product names
- Improved OCR accuracy

### **Resource Usage:**
- **Memory**: +~15MB for larger word list
- **CPU**: Minimal (sync runs weekly)
- **Network**: ~5MB data transfer per sync
- **Storage**: Negligible log file growth

## 🎯 Quick Start Guide

### **For Testing:**
```bash
# 1. Activate environment
env_ocr\Scripts\activate

# 2. Test everything
python scripts/sync_root_names.py --all
```

### **For Production:**
1. **Start application**: `uvicorn src.api:app --host 0.0.0.0 --port 8088`
2. **Scheduler auto-starts**: Weekly sync begins automatically
3. **Monitor via API**: Use `/root_names_sync/status` endpoint
4. **Manual sync if needed**: Use `/root_names_sync/run` endpoint

---

## 📝 Notes

- **Timezone**: All schedules use UTC
- **Authentication**: All API endpoints require valid JWT tokens
- **Backup**: Original CORRECT_WORDS preserved in constants.py
- **Rollback**: Can revert imports if needed
- **Scalability**: System handles 150k+ product names efficiently
