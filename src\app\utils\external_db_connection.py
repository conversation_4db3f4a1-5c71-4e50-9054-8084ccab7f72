"""
External Database Connection Utility

This module handles connections to the external PostgreSQL database
for fetching product root names from winproduit_master database.
"""

import logging
from contextlib import contextmanager
from typing import Generator, List, Optional

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError, OperationalError

from src.app.config import EXTERNAL_DATABASE_URL

logger = logging.getLogger(__name__)


class ExternalDatabaseManager:
    """
    Manages external PostgreSQL database connections for product root names sync.
    """

    def __init__(self):
        self.engine = None
        self.session_factory = None
        self._initialize_connection()

    def _initialize_connection(self):
        """Initialize external database connection."""
        try:
            # Create PostgreSQL engine with connection pooling
            self.engine = create_engine(
                EXTERNAL_DATABASE_URL,
                pool_size=5,
                max_overflow=10,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False  # Set to True for SQL debugging
            )

            # Test connection
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            # Create session factory
            self.session_factory = sessionmaker(
                bind=self.engine,
                autocommit=False,
                autoflush=False
            )

            logger.info("External PostgreSQL connection initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize external PostgreSQL connection: {e}")
            raise

    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """Get external database session with automatic cleanup."""
        if not self.session_factory:
            raise RuntimeError("External database not available")

        session = self.session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"External database session error: {e}")
            raise
        finally:
            session.close()

    def health_check(self) -> dict:
        """Check the health of the external PostgreSQL database connection."""
        status = {'available': False, 'error': None}

        if self.engine:
            try:
                with self.engine.connect() as conn:
                    conn.execute(text("SELECT 1"))
                status['available'] = True
            except Exception as e:
                status['error'] = str(e)

        return status

    def fetch_product_root_names(self) -> List[str]:
        """
        Fetch distinct product root names from the external database.
        
        Returns:
            List[str]: List of distinct product root names
        """
        try:
            with self.get_session() as session:
                # Execute the query to get distinct product root names
                result = session.execute(
                    text("SELECT DISTINCT p.nom_racine FROM produit p WHERE p.nom_racine IS NOT NULL AND p.nom_racine != ''")
                )
                
                # Extract the root names from the result
                root_names = [row[0] for row in result.fetchall() if row[0] and str(row[0]).strip()]
                
                logger.info(f"Successfully fetched {len(root_names)} product root names from external database")
                return root_names
                
        except Exception as e:
            logger.error(f"Failed to fetch product root names from external database: {e}")
            raise

    def test_connection_and_query(self) -> dict:
        """
        Test the connection and query to ensure everything works.
        
        Returns:
            dict: Test results with status and sample data
        """
        try:
            with self.get_session() as session:
                # Test basic connection
                session.execute(text("SELECT 1"))
                
                # Test the actual query with limit
                result = session.execute(
                    text("SELECT DISTINCT p.nom_racine FROM produit p WHERE p.nom_racine IS NOT NULL AND p.nom_racine != '' LIMIT 10")
                )
                
                sample_names = [row[0] for row in result.fetchall()]
                
                # Get total count
                count_result = session.execute(
                    text("SELECT COUNT(DISTINCT p.nom_racine) FROM produit p WHERE p.nom_racine IS NOT NULL AND p.nom_racine != ''")
                )
                total_count = count_result.scalar()
                
                return {
                    'status': 'success',
                    'total_count': total_count,
                    'sample_names': sample_names,
                    'connection_healthy': True
                }
                
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'connection_healthy': False
            }

    def close_connection(self):
        """Close the database connection."""
        if self.engine:
            self.engine.dispose()
            logger.info("External database connection closed")


# Global external database manager instance
external_db_manager = ExternalDatabaseManager()


def get_external_session() -> Generator[Session, None, None]:
    """Get external database session."""
    with external_db_manager.get_session() as session:
        yield session


def fetch_product_root_names() -> List[str]:
    """
    Convenience function to fetch product root names.
    
    Returns:
        List[str]: List of distinct product root names
    """
    return external_db_manager.fetch_product_root_names()


def test_external_connection() -> dict:
    """
    Convenience function to test external database connection.
    
    Returns:
        dict: Test results
    """
    return external_db_manager.test_connection_and_query()
