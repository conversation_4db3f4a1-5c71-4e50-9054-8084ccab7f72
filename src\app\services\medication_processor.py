import re
from fuzzywuzzy import fuzz, process
import logging
from src.app.utils import constants
from src.app.utils.root_names_constants import CORRECT_WORDS

logger = logging.getLogger(__name__)


def process_medication_text(ocr_text: str) -> str:
    """
    Process OCR text to extract and order components into a correct designation.
    Order: root name -> Dosage -> Présentation -> additional text
    """
    logger.info(f"Processing OCR text: {ocr_text}")
    original_text = ocr_text

    # Step 1: Extract Dosage
    dosage_match = constants.dosage_volume_pattern.search(original_text)
    dosage = dosage_match.group(0) if dosage_match else None
    if dosage:
        ocr_text = original_text.replace(dosage, "", 1).strip()

    # Step 2: Extract Présentation
    présentation = None
    for pattern in constants.patterns_presentation:
        match = pattern.search(ocr_text)
        if match:
            présentation = match.group(0)
            ocr_text = ocr_text.replace(présentation, "", 1).strip()
            break
    # Enhance to capture "30 comprimés pelliculés" (not fully matched by current patterns)
    if not présentation and "comprimés" in ocr_text.lower():
        comp_match = re.search(r'\b(\d+)\s*comprimés(?:\s*pelliculés)?\b', ocr_text, re.IGNORECASE)
        if comp_match:
            présentation = comp_match.group(0)
            ocr_text = ocr_text.replace(présentation, "", 1).strip()

    # Step 3: Extract Root Name with Fuzzy Matching
    words = ocr_text.split()
    best_score = 0
    best_match = None
    best_correct_word = None

    # Search entire text for sequences of 1-3 words
    for i in range(len(words)):
        for j in range(i + 1, min(i + 4, len(words) + 1)):
            phrase = " ".join(words[i:j])
            for correct_word in CORRECT_WORDS:
                score = fuzz.ratio(phrase.lower(), correct_word.lower())
                if score > best_score and score > 80:  # Threshold of 80%
                    best_score = score
                    best_match = phrase
                    best_correct_word = correct_word

    root_name = best_correct_word if best_match else None
    remaining_text = ocr_text
    if best_match and root_name:
        remaining_text = ocr_text.replace(best_match, "", 1).strip()

    # Step 4: Remove words that are just numbers > 5 chars
    words = remaining_text.split()
    filtered_words = [w for w in words if not (w.isdigit() and len(w) > 5)]
    remaining_text = " ".join(filtered_words)

    # Step 5: Additional text is what's left
    additional_text = remaining_text

    # Step 6: Construct the designation
    components = []
    if root_name:
        components.append(root_name)
    if dosage:
        components.append(dosage)
    if présentation:
        components.append(présentation)
    if additional_text:
        components.append(additional_text)

    designation = " ".join(components).strip()
    logger.info(f"Processed text: {designation}")
    return designation


def extract_dosage(text):
    """Extract dosage information from text using regex patterns."""
    # First check for dosage at the beginning of the string
    dosage_volume_pattern_start = re.compile(
        r'^\s*(\d+(?:\.\d+)?)\s*(ÂµG|AµG|µg|mcg|ug|mg|g|gr|gram|gramm|gramme|MICROGRAMMES|grammes|ui|mol|mmol|tab|mui|ch|ml|%|litre|litres|microg|micro|\bch\b)(?=\s|$|/|\d)',
        re.IGNORECASE)

    match_start = dosage_volume_pattern_start.search(text)
    if match_start:
        return match_start.group(0).strip()

    # Then check for dosage anywhere in the string
    dosage_volume_pattern = re.compile(
        r'(\d+(?:\.\d+)?)\s*(ÂµG|AµG|µg|mcg|ug|mg|g|gr|gram|gramm|gramme|MICROGRAMMES|grammes|ui|mol|mmol|tab|mui|ch|ml|%|litre|litres|microg|micro|\bch\b)(?=\s|$|/|\d)',
        re.IGNORECASE)

    match = dosage_volume_pattern.search(text)
    if match:
        # Get the full dosage text (number + unit)
        dosage = match.group(0).strip()
        return dosage
    return None


def extract_presentation(text):
    """Extract presentation information from text using regex patterns."""
    # First look for common presentation patterns with numbers
    presentation_patterns = [
        # Look for "X comprimés" pattern
        re.compile(r'\b(\d+)\s*(?:comprimés?|gélules?|capsules?|sachets?|ampoules?|flacons?|doses?|unidoses?)\b', re.IGNORECASE),
        # Look for "comprimés X" pattern
        re.compile(r'\b(?:comprimés?|gélules?|capsules?|sachets?|ampoules?|flacons?|doses?|unidoses?)\s*(\d+)\b', re.IGNORECASE),
        # Standard box patterns
        re.compile(r'\b(B(?:TE)?|BO(?:ITE)?|BT|B)[\s/]*(DE)?\s*(\d+)\b', re.IGNORECASE),
        re.compile(r'\b(\d+)\s*(?:CPS|CPR|cps|cp)\b', re.IGNORECASE),
        re.compile(r'\bB(?:TE)?/(\d+)CP\b', re.IGNORECASE),
        re.compile(r'\b(BT|BTE|B)\s*/\s*(\d+)', re.IGNORECASE),
        re.compile(r'\b(\d+)\s*(?:DOSES|DOSE|UD|UNIDOSE|UNIDOSES)\b', re.IGNORECASE),
        re.compile(r'\b(X\s?\d+|\d+\s*cp)\b', re.IGNORECASE),
        re.compile(r'\bX\s*(\d+)\b', re.IGNORECASE),
        re.compile(r'\b(B(?:TE)?|BO(?:ITE)?|BT|B)[\s/]*(\d+)\b', re.IGNORECASE)
    ]

    # Check each pattern
    for pattern in presentation_patterns:
        match = pattern.search(text)
        if match:
            return match.group(0).strip()

    # Check for simple presentation words
    presentation_words = ['GTTE', 'DISP', 'DOSES', 'DOSE', 'UD', 'UNIDOSE', 'UNIDOSES',
                          'BT/', 'BT', 'BTE', 'BTE/', 'CP', 'CPS', 'CPR', 'BOITE',
                          'COMPRIMÉS', 'COMPRIMÉ', 'GÉLULES', 'GÉLULE', 'CAPSULES', 'CAPSULE',
                          'SACHETS', 'SACHET', 'AMPOULES', 'AMPOULE', 'FLACONS', 'FLACON']

    for word in presentation_words:
        if f" {word} " in f" {text} ":
            # Find the word with surrounding context
            pattern = re.compile(r'\b\d+\s+' + re.escape(word) + r'\b|\b' + re.escape(word) + r'\s+\d+\b',
                                 re.IGNORECASE)
            match = pattern.search(text)
            if match:
                return match.group(0).strip()

    return None


def extract_root_name(text):
    """
    Extract the root name of the medication using fuzzy matching.
    Handles compound names and hyphenated names.

    Args:
        text (str): Text to search for root name

    Returns:
        tuple: (extracted_root_name, remaining_text)
    """
    # First, check if any of the known medication names appear exactly in the text
    for correct_word in constants.CORRECT_WORDS:
        if correct_word.upper() in text.upper():
            # Find the actual case in the original text
            pattern = re.compile(re.escape(correct_word), re.IGNORECASE)
            match = pattern.search(text)
            if match:
                actual_word = text[match.start():match.end()]
                remaining_text = text.replace(actual_word, "", 1).strip()
                return actual_word, remaining_text

    # If no exact match, try to identify hyphenated or compound names
    words = text.split()

    # Try combinations of 2-3 consecutive words to find medication names
    for i in range(len(words)):
        # Try 2-word combinations
        if i < len(words) - 1:
            two_word_combo = f"{words[i]} {words[i + 1]}"
            # Check if this combo is similar to any known medication
            matches = process.extract(
                two_word_combo,
                CORRECT_WORDS,
                scorer=fuzz.ratio,
                limit=3
            )
            if matches and max(matches, key=lambda x: x[1])[1] >= 80:
                remaining_text = text.replace(two_word_combo, "", 1).strip()
                return two_word_combo, remaining_text

        # Try 3-word combinations
        if i < len(words) - 2:
            three_word_combo = f"{words[i]} {words[i + 1]} {words[i + 2]}"
            matches = process.extract(
                three_word_combo,
                CORRECT_WORDS,
                scorer=fuzz.ratio,
                limit=3
            )
            if matches and max(matches, key=lambda x: x[1])[1] >= 80:
                remaining_text = text.replace(three_word_combo, "", 1).strip()
                return three_word_combo, remaining_text

        # Try with hyphenated names
        if "-" in words[i]:
            matches = process.extract(
                words[i],
                CORRECT_WORDS,
                scorer=fuzz.ratio,
                limit=3
            )
            if matches and max(matches, key=lambda x: x[1])[1] >= 80:
                remaining_text = text.replace(words[i], "", 1).strip()
                return words[i], remaining_text

    # If still no match, try individual words with fuzzy matching
    best_match = None
    best_score = 0
    best_word = None

    for word in words:
        # Skip very short words, numbers, or common words
        if len(word) < 3 or word.isdigit() or word.upper() in constants.content_correction:
            continue

        # Perform fuzzy matching
        matches = process.extract(
            word,
            CORRECT_WORDS,
            scorer=fuzz.ratio,
            limit=5
        )

        # Find the best match for this word
        if matches:
            top_match = max(matches, key=lambda x: x[1])
            if top_match[1] > best_score and top_match[1] >= 75:  # 75% threshold
                best_score = top_match[1]
                best_match = top_match[0]
                best_word = word

    if best_match:
        # Use the matched word from constants if it's a good match
        remaining_text = text.replace(best_word, "", 1).strip()
        return best_match, remaining_text

    # Fallback: use the first word that's not in the exclusion list
    for word in words:
        if word.upper() not in constants.content_correction and len(word) > 2 and not word.isdigit():
            remaining_text = text.replace(word, "", 1).strip()
            return word, remaining_text

    # Last resort: use the first word
    if words:
        return words[0], " ".join(words[1:])

    return "", text


def clean_remaining_text(text):
    """
    Clean the remaining text by removing numeric codes and irrelevant words.

    Args:
        text (str): Text to clean

    Returns:
        str: Cleaned text
    """
    # Remove words that are just numbers and longer than 5 characters
    words = text.split()
    cleaned_words = []

    # Define patterns to remove
    lot_pattern = re.compile(r'\bLOT[\s:]*[A-Z0-9]+\b', re.IGNORECASE)
    exp_pattern = re.compile(r'\b(?:EXP|PER)[\s:]*[0-9]{1,2}[-/][0-9]{1,2}(?:[-/][0-9]{2,4})?\b', re.IGNORECASE)
    price_pattern = re.compile(r'\b(?:PPV|PRIX)[\s:]*[0-9]+(?:[,.][0-9]+)?(?:\s*[A-Z]{2,3})?\b', re.IGNORECASE)

    # Remove lot numbers, expiration dates, and prices
    text = lot_pattern.sub('', text)
    text = exp_pattern.sub('', text)
    text = price_pattern.sub('', text)

    # Re-split after removing patterns
    words = text.split()

    for word in words:
        # Skip long numeric codes (likely barcodes)
        if word.isdigit() and len(word) > 5:
            continue

        # Skip words in the exclusion list if they're alone (not part of a phrase)
        if word.upper() in constants.content_correction and len(word) < 5:
            continue

        # Keep other words
        cleaned_words.append(word)

    # Remove common marketing phrases
    cleaned_text = " ".join(cleaned_words)
    marketing_phrases = [
        "ECHANTILLON GRATUIT",
        "VENTE INTERDITE",
        "NE PEUT ETRE VENDU SEPAREMENT",
        "MICROGRANULES",
        "VENTE",
        "PRIX CONSEILLE",
        "PRIX",
        "REMBOURSABLE",
        "NON REMBOURSABLE",
        "REMBOURSÉ",
        "NON REMBOURSÉ",
        "VOIE ORALE",
        "VOIE CUTANEE",
        "VOIE INJECTABLE"
    ]

    for phrase in marketing_phrases:
        pattern = re.compile(re.escape(phrase), re.IGNORECASE)
        cleaned_text = pattern.sub("", cleaned_text)

    # Remove extra spaces
    cleaned_text = " ".join(cleaned_text.split())

    return cleaned_text


def construct_designation(root_name, dosage, presentation, additional_text, active_ingredients):
    """
    Construct the final medication designation in the correct order.

    Args:
        root_name (str): Root name of the medication
        dosage (str): Dosage information
        presentation (str): Presentation information
        additional_text (str): Additional relevant text
        active_ingredients (list): List of active ingredients

    Returns:
        str: Structured medication designation
    """
    components = []

    # Add root name (required)
    if root_name:
        components.append(root_name)

    # Add dosage if available
    if dosage:
        components.append(dosage)

    # Add presentation if available
    if presentation:
        components.append(presentation)

    # Add additional text if not empty
    if additional_text and additional_text.strip():
        components.append(additional_text.strip())

    # Add active ingredients at the end
    if active_ingredients:
        components.append(" ".join(active_ingredients))

    # Join all components with spaces
    return " ".join(components)


def remove_pattern_from_text(text, pattern):
    """Remove a pattern from text and clean up extra spaces."""
    cleaned_text = re.sub(pattern, "", text, flags=re.IGNORECASE)
    return " ".join(cleaned_text.split())


def is_valid_word(word):
    """Check if a word is valid for consideration as a root name."""
    # Skip very short words
    if len(word) < 3:
        return False

    # Skip words that are just numbers
    if word.isdigit():
        return False

    # Skip words that are in the exclusion list
    if word.upper() in constants.content_correction:
        return False

    return True
