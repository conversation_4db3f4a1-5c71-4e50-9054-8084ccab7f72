"""
Scheduler Service

This service manages scheduled tasks using APScheduler.
Currently handles the weekly synchronization of product root names.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
from apscheduler.jobstores.memory import MemoryJobStore

from src.app.services.root_names_sync_service import perform_sync

logger = logging.getLogger(__name__)


class SchedulerService:
    """Service for managing scheduled tasks."""

    def __init__(self):
        self.scheduler = None
        self.is_running = False
        self.job_results = {}
        self._initialize_scheduler()

    def _initialize_scheduler(self):
        """Initialize the APScheduler."""
        try:
            # Configure job stores
            jobstores = {
                'default': MemoryJobStore()
            }

            # Configure executors
            job_defaults = {
                'coalesce': False,
                'max_instances': 1,
                'misfire_grace_time': 300  # 5 minutes
            }

            # Create scheduler
            self.scheduler = BackgroundScheduler(
                jobstores=jobstores,
                job_defaults=job_defaults,
                timezone='UTC'
            )

            # Add event listeners
            self.scheduler.add_listener(self._job_executed_listener, EVENT_JOB_EXECUTED)
            self.scheduler.add_listener(self._job_error_listener, EVENT_JOB_ERROR)

            logger.info("Scheduler initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize scheduler: {e}")
            raise

    def _job_executed_listener(self, event):
        """Handle job execution events."""
        job_id = event.job_id
        logger.info(f"Job {job_id} executed successfully")
        
        # Store job execution result
        self.job_results[job_id] = {
            'status': 'success',
            'executed_at': datetime.now().isoformat(),
            'job_id': job_id
        }

    def _job_error_listener(self, event):
        """Handle job error events."""
        job_id = event.job_id
        exception = event.exception
        logger.error(f"Job {job_id} failed with error: {exception}")
        
        # Store job error result
        self.job_results[job_id] = {
            'status': 'error',
            'executed_at': datetime.now().isoformat(),
            'job_id': job_id,
            'error': str(exception)
        }

    def start_scheduler(self):
        """Start the scheduler."""
        try:
            if not self.is_running:
                self.scheduler.start()
                self.is_running = True
                logger.info("Scheduler started successfully")
            else:
                logger.warning("Scheduler is already running")
        except Exception as e:
            logger.error(f"Failed to start scheduler: {e}")
            raise

    def stop_scheduler(self):
        """Stop the scheduler."""
        try:
            if self.is_running:
                self.scheduler.shutdown(wait=False)
                self.is_running = False
                logger.info("Scheduler stopped successfully")
            else:
                logger.warning("Scheduler is not running")
        except Exception as e:
            logger.error(f"Failed to stop scheduler: {e}")
            raise

    def add_root_names_sync_job(self, 
                               day_of_week: str = 'sunday', 
                               hour: int = 2, 
                               minute: int = 0) -> str:
        """
        Add the weekly root names synchronization job.
        
        Args:
            day_of_week (str): Day of the week to run (default: 'sunday')
            hour (int): Hour to run (default: 2 AM)
            minute (int): Minute to run (default: 0)
            
        Returns:
            str: Job ID
        """
        job_id = 'root_names_sync_weekly'
        
        try:
            # Remove existing job if it exists
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
                logger.info(f"Removed existing job {job_id}")

            # Create cron trigger for weekly execution
            trigger = CronTrigger(
                day_of_week=day_of_week,
                hour=hour,
                minute=minute,
                timezone='UTC'
            )

            # Add the job
            job = self.scheduler.add_job(
                func=self._sync_root_names_job,
                trigger=trigger,
                id=job_id,
                name='Weekly Root Names Sync',
                replace_existing=True
            )

            logger.info(f"Added weekly root names sync job: {job_id}")
            logger.info(f"Next run time: {job.next_run_time}")
            
            return job_id

        except Exception as e:
            logger.error(f"Failed to add root names sync job: {e}")
            raise

    def _sync_root_names_job(self):
        """Job function for root names synchronization."""
        try:
            logger.info("Starting scheduled root names synchronization...")
            result = perform_sync()
            
            if result.get('success', False):
                logger.info("Scheduled root names synchronization completed successfully")
            else:
                logger.error(f"Scheduled root names synchronization failed: {result.get('message', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"Error in scheduled root names synchronization: {e}")
            raise

    def run_sync_now(self) -> Dict[str, Any]:
        """
        Run the root names sync immediately (outside of schedule).
        
        Returns:
            Dict[str, Any]: Sync results
        """
        try:
            logger.info("Running root names sync manually...")
            result = perform_sync()
            
            # Store manual execution result
            self.job_results['manual_sync'] = {
                'status': 'success' if result.get('success', False) else 'error',
                'executed_at': datetime.now().isoformat(),
                'job_id': 'manual_sync',
                'result': result
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Manual sync failed: {e}")
            
            # Store manual execution error
            self.job_results['manual_sync'] = {
                'status': 'error',
                'executed_at': datetime.now().isoformat(),
                'job_id': 'manual_sync',
                'error': str(e)
            }
            
            return {
                'success': False,
                'message': f'Manual sync failed: {e}'
            }

    def get_job_status(self, job_id: str = 'root_names_sync_weekly') -> Dict[str, Any]:
        """
        Get the status of a specific job.
        
        Args:
            job_id (str): Job ID to check
            
        Returns:
            Dict[str, Any]: Job status information
        """
        try:
            job = self.scheduler.get_job(job_id)
            
            if job:
                return {
                    'job_id': job_id,
                    'name': job.name,
                    'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger),
                    'last_result': self.job_results.get(job_id, None),
                    'is_scheduled': True
                }
            else:
                return {
                    'job_id': job_id,
                    'is_scheduled': False,
                    'message': 'Job not found'
                }
                
        except Exception as e:
            logger.error(f"Failed to get job status for {job_id}: {e}")
            return {
                'job_id': job_id,
                'is_scheduled': False,
                'error': str(e)
            }

    def get_all_jobs_status(self) -> Dict[str, Any]:
        """
        Get status of all scheduled jobs.
        
        Returns:
            Dict[str, Any]: Status of all jobs
        """
        try:
            jobs = self.scheduler.get_jobs()
            
            jobs_status = []
            for job in jobs:
                jobs_status.append({
                    'job_id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger),
                    'last_result': self.job_results.get(job.id, None)
                })
            
            return {
                'scheduler_running': self.is_running,
                'total_jobs': len(jobs),
                'jobs': jobs_status,
                'job_results_history': self.job_results
            }
            
        except Exception as e:
            logger.error(f"Failed to get all jobs status: {e}")
            return {
                'scheduler_running': self.is_running,
                'error': str(e)
            }


# Global scheduler service instance
scheduler_service = SchedulerService()


def start_scheduler():
    """Start the global scheduler service."""
    scheduler_service.start_scheduler()


def stop_scheduler():
    """Stop the global scheduler service."""
    scheduler_service.stop_scheduler()


def setup_weekly_sync(day_of_week: str = 'sunday', hour: int = 2, minute: int = 0) -> str:
    """
    Setup weekly root names synchronization.
    
    Args:
        day_of_week (str): Day of the week to run
        hour (int): Hour to run
        minute (int): Minute to run
        
    Returns:
        str: Job ID
    """
    return scheduler_service.add_root_names_sync_job(day_of_week, hour, minute)


def run_sync_now() -> Dict[str, Any]:
    """Run sync immediately."""
    return scheduler_service.run_sync_now()


def get_scheduler_status() -> Dict[str, Any]:
    """Get scheduler status."""
    return scheduler_service.get_all_jobs_status()
